"""
数据库查询优化器 - 提供高性能的数据库操作
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Tuple, Type

from tortoise.models import Model
from tortoise.queryset import QuerySet

from .logging_config import ServiceLogger


class DatabaseOptimizer:
    """数据库查询优化器"""
    
    def __init__(self, logger: ServiceLogger):
        self.logger = logger
        self._query_cache = {}
        self._cache_ttl = 300  # 5分钟缓存
    
    async def batch_exists_check(
        self, 
        model: Type[Model], 
        field_name: str, 
        values: List[Any],
        additional_filters: Optional[Dict] = None
    ) -> Set[Any]:
        """
        批量检查记录是否存在
        
        Args:
            model: 模型类
            field_name: 字段名
            values: 要检查的值列表
            additional_filters: 额外的过滤条件
            
        Returns:
            Set[Any]: 存在的值集合
        """
        if not values:
            return set()
        
        start_time = datetime.now()
        
        try:
            # 构建查询条件
            filters = {f"{field_name}__in": values}
            if additional_filters:
                filters.update(additional_filters)
            
            # 执行批量查询
            existing_values = await model.filter(**filters).values_list(field_name, flat=True)
            
            duration = (datetime.now() - start_time).total_seconds()
            self.logger.log_database_operation(
                "batch_exists_check",
                model._meta.db_table,
                len(existing_values),
                duration
            )
            
            return set(existing_values)
            
        except Exception as e:
            self.logger.log_operation_error("batch_exists_check", e)
            raise
    
    async def batch_create_optimized(
        self, 
        model: Type[Model], 
        records: List[Model],
        batch_size: int = 1000
    ) -> int:
        """
        优化的批量创建
        
        Args:
            model: 模型类
            records: 要创建的记录列表
            batch_size: 批次大小
            
        Returns:
            int: 创建的记录数量
        """
        if not records:
            return 0
        
        start_time = datetime.now()
        total_created = 0
        
        try:
            # 分批创建以避免内存和数据库压力
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                await model.bulk_create(batch)
                total_created += len(batch)
                
                # 短暂休息以减少数据库压力
                if i + batch_size < len(records):
                    await asyncio.sleep(0.01)
            
            duration = (datetime.now() - start_time).total_seconds()
            self.logger.log_database_operation(
                "batch_create_optimized",
                model._meta.db_table,
                total_created,
                duration
            )
            
            return total_created
            
        except Exception as e:
            self.logger.log_operation_error("batch_create_optimized", e)
            raise
    
    async def batch_update_optimized(
        self, 
        model: Type[Model], 
        updates: List[Tuple[Dict, Dict]],  # [(filters, update_data), ...]
        batch_size: int = 500
    ) -> int:
        """
        优化的批量更新
        
        Args:
            model: 模型类
            updates: 更新操作列表，每个元素是(过滤条件, 更新数据)的元组
            batch_size: 批次大小
            
        Returns:
            int: 更新的记录数量
        """
        if not updates:
            return 0
        
        start_time = datetime.now()
        total_updated = 0
        
        try:
            # 分批更新
            for i in range(0, len(updates), batch_size):
                batch = updates[i:i + batch_size]
                
                for filters, update_data in batch:
                    updated_count = await model.filter(**filters).update(**update_data)
                    total_updated += updated_count
                
                # 短暂休息
                if i + batch_size < len(updates):
                    await asyncio.sleep(0.01)
            
            duration = (datetime.now() - start_time).total_seconds()
            self.logger.log_database_operation(
                "batch_update_optimized",
                model._meta.db_table,
                total_updated,
                duration
            )
            
            return total_updated
            
        except Exception as e:
            self.logger.log_operation_error("batch_update_optimized", e)
            raise
    
    async def get_or_create_batch(
        self, 
        model: Type[Model], 
        items: List[Dict],
        unique_fields: List[str],
        batch_size: int = 1000
    ) -> Tuple[int, int]:
        """
        批量获取或创建记录
        
        Args:
            model: 模型类
            items: 要处理的项目列表
            unique_fields: 唯一字段列表
            batch_size: 批次大小
            
        Returns:
            Tuple[int, int]: (创建数量, 已存在数量)
        """
        if not items:
            return 0, 0
        
        start_time = datetime.now()
        created_count = 0
        existing_count = 0
        
        try:
            # 分批处理
            for i in range(0, len(items), batch_size):
                batch = items[i:i + batch_size]
                
                # 构建查询条件检查已存在的记录
                if len(unique_fields) == 1:
                    field = unique_fields[0]
                    values = [item[field] for item in batch if field in item]
                    existing_values = await self.batch_exists_check(model, field, values)
                    
                    # 分离新记录和已存在记录
                    new_items = []
                    for item in batch:
                        if item.get(field) in existing_values:
                            existing_count += 1
                        else:
                            new_items.append(model(**item))
                else:
                    # 多字段唯一性检查（较复杂，逐个检查）
                    new_items = []
                    for item in batch:
                        filters = {field: item[field] for field in unique_fields if field in item}
                        exists = await model.filter(**filters).exists()
                        
                        if exists:
                            existing_count += 1
                        else:
                            new_items.append(model(**item))
                
                # 批量创建新记录
                if new_items:
                    await model.bulk_create(new_items)
                    created_count += len(new_items)
                
                # 短暂休息
                if i + batch_size < len(items):
                    await asyncio.sleep(0.01)
            
            duration = (datetime.now() - start_time).total_seconds()
            self.logger.log_database_operation(
                "get_or_create_batch",
                model._meta.db_table,
                created_count + existing_count,
                duration
            )
            
            return created_count, existing_count
            
        except Exception as e:
            self.logger.log_operation_error("get_or_create_batch", e)
            raise
    
    def clear_cache(self):
        """清理查询缓存"""
        self._query_cache.clear()
    
    async def optimize_query(self, queryset: QuerySet) -> QuerySet:
        """
        优化查询集
        
        Args:
            queryset: 原始查询集
            
        Returns:
            QuerySet: 优化后的查询集
        """
        # 添加常用的优化
        return queryset.select_related().prefetch_related()


class ConnectionPoolManager:
    """数据库连接池管理器"""
    
    def __init__(self, logger: ServiceLogger):
        self.logger = logger
        self._pool_stats = {
            "active_connections": 0,
            "total_queries": 0,
            "slow_queries": 0
        }
    
    async def monitor_query_performance(self, query_func, threshold: float = 1.0):
        """
        监控查询性能
        
        Args:
            query_func: 查询函数
            threshold: 慢查询阈值（秒）
        """
        start_time = datetime.now()
        
        try:
            result = await query_func()
            duration = (datetime.now() - start_time).total_seconds()
            
            self._pool_stats["total_queries"] += 1
            
            if duration > threshold:
                self._pool_stats["slow_queries"] += 1
                self.logger.log_performance_metrics(
                    "slow_query_detected",
                    {"duration": duration, "threshold": threshold}
                )
            
            return result
            
        except Exception as e:
            self.logger.log_operation_error("query_execution", e)
            raise
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        return self._pool_stats.copy()
