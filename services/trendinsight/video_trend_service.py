# -*- coding: utf-8 -*-
"""
TrendInsight 视频趋势服务

负责处理视频趋势数据的获取和存储
"""

import logging
import random
from datetime import datetime, timedelta

from rpc.trendinsight.exceptions import TrendInsightError

from controllers.trendinsight.main_controller import trendinsight_main_controller
from models.trendinsight.models import update_trendinsight_video


# 添加日志记录器
logger = logging.getLogger(__name__)


class VideoTrendService:
    """TrendInsight 视频趋势服务类"""
    
    @staticmethod
    async def fetch_and_store_video_trend_score(aweme_id: str, controller=None) -> bool:
        """
        获取视频趋势指数并存储到数据库

        调用 TrendInsight API 获取视频指数数据，提取 trend 数组中最后一项的值作为趋势评分，
        并将其与 aweme_id 对应存入 trendinsight_video 表

        Args:
            aweme_id: 抖音视频ID

        Returns:
            bool: 操作是否成功
        """
        try:
            logger.info(f"fetch_and_store_video_trend_score: 开始获取视频趋势数据 - aweme_id: {aweme_id}")

            # 生成日期范围（从前天开始算的7天）
            end_date = datetime.now() - timedelta(days=2)  # 前天
            start_date = end_date - timedelta(days=7)  # 前天往前推7天

            start_date_str = start_date.strftime("%Y%m%d")
            end_date_str = end_date.strftime("%Y%m%d")

            logger.info(f"fetch_and_store_video_trend_score: 查询日期范围 {start_date_str} - {end_date_str}")

            # 使用传入的控制器或默认控制器
            if controller is None:
                from controllers.trendinsight.main_controller import trendinsight_main_controller
                controller = trendinsight_main_controller

            # 调用 get_item_index_exist 获取视频指数数据
            try:
                video_index_response = await controller.get_item_index_exist(
                    item_id=aweme_id
                )

                if not video_index_response.is_success:
                    logger.error(f"fetch_and_store_video_trend_score: API 调用失败 - status: {video_index_response.status}")
                    return False

                # 检查是否有趋势数据
                trend_data = video_index_response.trend_data
                if not trend_data:
                    logger.warning(f"fetch_and_store_video_trend_score: 没有趋势数据 - aweme_id: {aweme_id}")
                    return False

                # 获取最后一项的值作为趋势评分
                last_trend_item = trend_data[-1]
                trend_score_str = last_trend_item.value

                # 将字符串转换为浮点数
                try:
                    trend_score = float(trend_score_str)
                except (ValueError, TypeError):
                    logger.error(f"fetch_and_store_video_trend_score: 趋势值转换失败 - value: {trend_score_str}")
                    trend_score = 0.0

                logger.info(
                    f"fetch_and_store_video_trend_score: 获取到趋势评分 - {trend_score} (日期: {last_trend_item.datetime})"
                )

                # 存储到数据库
                video_data = {"aweme_id": aweme_id, "trend_score": trend_score}

                success = await update_trendinsight_video(video_data)
                if success:
                    logger.info(
                        f"fetch_and_store_video_trend_score: 成功存储趋势数据 - aweme_id: {aweme_id}, score: {trend_score}"
                    )
                    return True
                else:
                    logger.error(f"fetch_and_store_video_trend_score: 存储失败 - aweme_id: {aweme_id}")
                    return False

            except TrendInsightError as e:
                logger.error(f"fetch_and_store_video_trend_score: TrendInsight API 错误 - {e}")
                return False
            except Exception as e:
                logger.error(f"fetch_and_store_video_trend_score: API 调用异常 - {e}")
                return False

        except Exception as e:
            logger.error(f"fetch_and_store_video_trend_score: 操作失败 - {e}")
            import traceback

            logger.error(traceback.format_exc())
            return False