"""
TrendInsight 控制器模块
"""

from .author_sync_controller import Author<PERSON>ync<PERSON><PERSON>roller
from .keyword_sync_controller import KeywordSyncController
from .main_controller import TrendInsightMainController
from ..douyin.video.video_process_controller import VideoProcessController

# 创建控制器实例，保持向后兼容
author_sync_controller = AuthorSyncController()
keyword_sync_controller = KeywordSyncController()
video_process_controller = VideoProcessController()
trendinsight_controller = TrendInsightMainController()

# 向后兼容的别名
TrendInsightController = TrendInsightMainController

__all__ = [
    "AuthorSyncController",
    "KeywordSyncController",
    "VideoProcessController",
    "TrendInsightMainController",
    "TrendInsightController",
    "author_sync_controller",
    "keyword_sync_controller",
    "video_process_controller",
    "trendinsight_controller",
]
