"""
TrendInsight 关键词同步控制器

负责同步关键词相关视频列表的业务逻辑，集成收件箱关联处理功能
"""

import hashlib
import logging
from typing import List, Optional, Tuple

from fastapi import HTTPException

from mappers.trendinsight import TrendInsightKeywordMapper, TrendInsightVideoMapper
from models.enums import KeywordActionType, Platform, SourceType
from models.trendinsight.models import TrendInsightKeyword, TrendInsightVideoRelated
from rpc.trendinsight import AsyncTrendInsightAPI, client_manager
from rpc.trendinsight.config import TrendInsightConfig
from rpc.trendinsight.schemas import VideoSearchResponse
from schemas.trendinsight import DouyinAwemeData, KeywordSyncResponse
from services.base import BaseService, ValidationError
from services.inbox import InboxService


class KeywordSyncController(BaseService):
    """TrendInsight 关键词同步控制器 - 集成收件箱关联处理功能"""

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化关键词同步控制器

        Args:
            logger: 日志记录器，如果不提供则使用默认配置
        """
        super().__init__(logger)
        self.config = TrendInsightConfig()
        self.inbox_service = InboxService(self.logger)

    def _calculate_keyword_hash(self, keyword: str) -> str:
        """
        计算关键词的哈希值

        Args:
            keyword: 关键词文本

        Returns:
            str: MD5 哈希值

        Raises:
            ValidationError: 当关键词为空或无效时
        """
        if not keyword or not keyword.strip():
            raise ValidationError("关键词不能为空")

        try:
            return hashlib.md5(keyword.strip().encode("utf-8")).hexdigest()
        except Exception as e:
            self.error_tracker.track_error("keyword_hash_calculation", e)
            raise ValidationError(f"计算关键词哈希失败: {str(e)}")

    async def _get_trendinsight_client(self) -> AsyncTrendInsightAPI:
        """
        获取 TrendInsight 异步客户端实例

        Returns:
            AsyncTrendInsightAPI: 配置好账号提供者的异步客户端实例

        Raises:
            HTTPException: 当客户端创建失败时
        """
        try:
            # 通过 client_manager 创建客户端，cookies 由账号提供者自动管理
            async_client = client_manager.create_async_client()
            return AsyncTrendInsightAPI(async_client=async_client)
        except Exception as e:
            self.error_tracker.track_error("trendinsight_client_creation", e)
            raise HTTPException(status_code=500, detail=f"创建 TrendInsight 客户端失败: {str(e)}")

    async def search_info_by_keyword(
        self,
        keyword: str,
        author_ids: Optional[List[str]] = None,
        category_id: str = "0",
        date_type: int = 0,
        label_type: int = 0,
        duration_type: int = 0,
    ) -> VideoSearchResponse:
        """
        关键字搜索视频接口

        Args:
            keyword: 搜索关键词
            author_ids: 作者ID列表，可选
            category_id: 分类ID，默认为"0"
            date_type: 日期类型，默认为0
            label_type: 标签类型，默认为0
            duration_type: 时长类型，默认为0

        Returns:
            VideoSearchResponse: 视频搜索响应

        Raises:
            HTTPException: 当搜索失败时
        """
        operation_id = self.performance_monitor.start_operation("search_info_by_keyword")

        try:
            # 参数验证
            self.validate_required_params(keyword=keyword)

            self.service_logger.log_business_event(
                "开始关键词视频搜索",
                {
                    "keyword": keyword,
                    "author_ids_count": len(author_ids) if author_ids else 0,
                    "category_id": category_id,
                    "date_type": date_type,
                    "label_type": label_type,
                    "duration_type": duration_type
                }
            )

            client = await self._get_trendinsight_client()

            response = await client.search_info_by_keyword(
                keyword=keyword,
                author_ids=author_ids,
                category_id=category_id,
                date_type=date_type,
                label_type=label_type,
                duration_type=duration_type,
            )

            self.service_logger.log_business_event(
                "关键词视频搜索完成",
                {
                    "keyword": keyword,
                    "success": response.is_success if response else False,
                    "video_count": len(response.video_items) if response and response.video_items else 0
                }
            )

            return response

        except Exception as e:
            self.error_tracker.track_error("search_info_by_keyword", e)
            raise HTTPException(status_code=500, detail=f"搜索视频失败: {str(e)}")
        finally:
            self.performance_monitor.finish_operation(operation_id)

    async def _process_video_data(
        self,
        video_items: List,
        keyword: str,
        sync_result: KeywordSyncResponse
    ) -> Tuple[List[DouyinAwemeData], List[str]]:
        """
        处理视频数据：转换、过滤和数据库操作

        Args:
            video_items: 原始视频项目列表
            keyword: 关键词
            sync_result: 同步结果对象

        Returns:
            Tuple[List[DouyinAwemeData], List[str]]: 过滤后的视频数据和视频ID列表
        """
        try:
            self.service_logger.log_business_event(
                "开始处理视频数据",
                {"keyword": keyword, "video_count": len(video_items)}
            )

            # 使用mapper转换数据为完整的DouyinAwemeData格式
            video_data_list, video_ids = TrendInsightVideoMapper.keyword_videos_to_douyin_aweme_data_list(
                videos=video_items, source_keyword=keyword
            )

            # 过滤出 index > 10 的视频数据（优化的函数式编程风格）
            filtered_items = [
                (i, v) for i, v in enumerate(video_data_list)
                if i < len(video_ids) and self._is_valid_video_index(v)
            ]

            # 解构过滤结果
            filtered_video_data_list: List[DouyinAwemeData] = [item[1] for item in filtered_items]
            filtered_video_ids: List[str] = [video_ids[item[0]] for item in filtered_items]

            self.service_logger.log_business_event(
                "视频数据过滤完成",
                {
                    "keyword": keyword,
                    "original_count": len(video_data_list),
                    "filtered_count": len(filtered_video_data_list)
                }
            )

            # 将过滤后的视频数据赋值给响应
            sync_result.video_items = filtered_video_data_list

            # 使用服务层处理数据库操作
            from controllers.trendinsight.services import DouyinAwemeService

            aweme_created, aweme_existing = await DouyinAwemeService.ensure_douyin_aweme_records(
                video_data_list=filtered_video_data_list, video_ids=filtered_video_ids
            )

            self.service_logger.log_business_event(
                "DouyinAweme 数据库处理完成",
                {
                    "keyword": keyword,
                    "created": aweme_created,
                    "existing": aweme_existing
                }
            )

            # 更新同步结果统计
            sync_result.videos_synced = len(filtered_video_ids)

            return filtered_video_data_list, filtered_video_ids

        except Exception as e:
            error_msg = f"DouyinAweme 表智能批量处理失败: {str(e)}"
            sync_result.errors.append(error_msg)
            self.error_tracker.track_error("video_data_processing", e)
            self.service_logger.log_operation_error("视频数据处理", e)
            return [], []

    def _is_valid_video_index(self, video: DouyinAwemeData) -> bool:
        """
        检查视频索引是否有效（index > 10）

        Args:
            video: 视频数据对象

        Returns:
            bool: 是否有效
        """
        try:
            if video.index is None:
                return False

            index_value = float(video.index) if isinstance(video.index, str) else video.index
            return index_value > 10
        except (ValueError, TypeError):
            return False

    async def _process_video_relations(
        self,
        keyword_record: TrendInsightKeyword,
        video_ids: List[str],
        sync_result: KeywordSyncResponse
    ) -> None:
        """
        处理视频关联关系

        Args:
            keyword_record: 关键词记录
            video_ids: 视频ID列表
            sync_result: 同步结果对象
        """
        try:
            self.service_logger.log_business_event(
                "开始处理关联关系",
                {"keyword_id": keyword_record.id, "video_count": len(video_ids)}
            )

            # 批量检查已存在的关联记录（性能优化：一次查询）
            existing_relations: List[str] = await TrendInsightVideoRelated.filter(
                source_id=keyword_record.id, video_id__in=video_ids
            ).values_list("video_id", flat=True)

            existing_video_ids: set[str] = set(existing_relations)
            sync_result.relations_existing = len(existing_video_ids)

            # 使用集合运算计算需要创建的新关联（性能优化）
            all_video_ids = set(video_ids)
            new_video_ids = all_video_ids - existing_video_ids

            self.service_logger.log_business_event(
                "关联关系分析完成",
                {
                    "keyword_id": keyword_record.id,
                    "existing_relations": len(existing_video_ids),
                    "new_relations_needed": len(new_video_ids)
                }
            )

            # 准备批量创建的数据
            if new_video_ids:
                new_relations: List[TrendInsightVideoRelated] = []
                for video_id in new_video_ids:
                    new_relation = TrendInsightVideoRelated(
                        source_type=SourceType.KEYWORD,
                        source_id=keyword_record.id,
                        video_id=video_id,
                        platform=Platform.DOUYIN,
                    )
                    new_relations.append(new_relation)

                # 批量创建新关联记录
                await TrendInsightVideoRelated.bulk_create(new_relations)
                sync_result.relations_created = len(new_relations)

                self.service_logger.log_business_event(
                    "关联关系创建完成",
                    {"keyword_id": keyword_record.id, "created_count": len(new_relations)}
                )
            else:
                sync_result.relations_created = 0
                self.service_logger.log_business_event(
                    "无需创建新关联",
                    {"keyword_id": keyword_record.id}
                )

        except Exception as e:
            error_msg = f"批量创建关键词关联记录失败: {str(e)}"
            sync_result.errors.append(error_msg)
            self.error_tracker.track_error("video_relations_processing", e)
            self.service_logger.log_operation_error("处理视频关联关系", e)

    async def _update_keyword_video_count(
        self,
        keyword_record: TrendInsightKeyword,
        video_ids: List[str],
        sync_result: KeywordSyncResponse
    ) -> None:
        """
        更新关键词视频计数

        Args:
            keyword_record: 关键词记录
            video_ids: 视频ID列表
            sync_result: 同步结果对象
        """
        try:
            current_video_count = len(video_ids)

            # 更新视频同步统计
            if sync_result.videos_synced == 0:
                sync_result.videos_synced = current_video_count

            # 智能更新关键词的视频总数
            if keyword_record.video_count != current_video_count:
                keyword_record.video_count = current_video_count
                await keyword_record.save()

                self.service_logger.log_business_event(
                    "关键词视频计数已更新",
                    {
                        "keyword_id": keyword_record.id,
                        "old_count": keyword_record.video_count,
                        "new_count": current_video_count
                    }
                )

                # 更新响应数据中的视频数量
                if sync_result.keyword_data:
                    sync_result.keyword_data = TrendInsightKeywordMapper.keyword_model_to_data(keyword_record)
            else:
                self.service_logger.log_business_event(
                    "关键词视频计数无变化",
                    {"keyword_id": keyword_record.id, "count": current_video_count}
                )

        except Exception as e:
            error_msg = f"更新关键词视频计数失败: {str(e)}"
            sync_result.errors.append(error_msg)
            self.error_tracker.track_error("keyword_video_count_update", e)
            self.service_logger.log_operation_error("更新关键词视频计数", e)

    async def _process_inbox_relation(
        self,
        keyword_record: TrendInsightKeyword,
        keyword: str,
        video_data_list: List[DouyinAwemeData],
        sync_result: KeywordSyncResponse
    ) -> None:
        """
        处理收件箱关联 - 为所有订阅了该关键词的用户处理收件箱关联

        Args:
            keyword_record: 关键词记录对象
            keyword: 关键词文本
            video_data_list: 视频数据列表
            sync_result: 同步结果对象
        """
        try:
            # 导入用户收件箱关联模型
            from models.qihaozhushou import UserInboxSourceRelated, SourceType

            self.service_logger.log_business_event(
                "开始查找关键词订阅用户",
                {
                    "keyword": keyword,
                    "keyword_id": keyword_record.id,
                    "video_count": len(video_data_list)
                }
            )

            # 1. 查找所有订阅了该关键词的用户
            subscribed_users = await UserInboxSourceRelated.filter(
                source_id=str(keyword_record.id),  # 关键词ID作为source_id
                source_type=SourceType.KEYWORD,  # 来源类型为关键词
                is_deleted=False  # 未删除的订阅
            ).values_list("user_uuid", flat=True)

            # 去重用户列表
            unique_users = list(set(subscribed_users))

            if not unique_users:
                self.service_logger.log_business_event(
                    "无用户订阅该关键词",
                    {"keyword": keyword, "keyword_id": keyword_record.id}
                )
                return

            self.service_logger.log_business_event(
                "找到关键词订阅用户",
                {
                    "keyword": keyword,
                    "keyword_id": keyword_record.id,
                    "user_count": len(unique_users),
                    "video_count": len(video_data_list)
                }
            )

            # 2. 为每个订阅用户处理收件箱关联
            processed_users = 0
            failed_users = 0

            for user_uuid in unique_users:
                try:
                    # 将 DouyinAwemeData 对象转换为字典格式，以便 InboxService 处理
                    raw_video_list = []
                    for video_data in video_data_list:
                        video_dict = {
                            "aweme_id": video_data.aweme_id,
                            "publish_time": video_data.create_time.timestamp() if video_data.create_time else None,
                            "title": video_data.title,
                            "desc": video_data.desc,
                            "nickname": video_data.nickname,
                            "liked_count": video_data.liked_count,
                            "comment_count": video_data.comment_count,
                            "share_count": video_data.share_count,
                            "collected_count": video_data.collected_count
                        }
                        raw_video_list.append(video_dict)

                    # 使用 InboxService 处理收件箱关联
                    inbox_result = await self.inbox_service.process_source_videos_by_params(
                        user_uuid=user_uuid,
                        source_id=str(keyword_record.id),
                        source_type=SourceType.KEYWORD.value,
                        raw_video_list=raw_video_list
                    )

                    if inbox_result:
                        processed_users += 1
                        self.service_logger.log_business_event(
                            "用户收件箱关联处理完成",
                            {
                                "user_uuid": user_uuid,
                                "keyword": keyword,
                                "keyword_id": keyword_record.id,
                                "processed_count": len(video_data_list),
                                "inbox_result": str(inbox_result)
                            }
                        )
                    else:
                        self.service_logger.log_business_event(
                            "用户收件箱关联处理无结果",
                            {
                                "user_uuid": user_uuid,
                                "keyword": keyword,
                                "keyword_id": keyword_record.id
                            }
                        )

                except Exception as user_error:
                    failed_users += 1
                    error_msg = f"用户 {user_uuid} 收件箱关联处理失败: {str(user_error)}"
                    sync_result.errors.append(error_msg)
                    self.error_tracker.track_error("user_inbox_relation_processing", user_error)
                    self.service_logger.log_operation_error(f"用户 {user_uuid} 收件箱关联处理", user_error)

            # 3. 记录总体处理结果
            self.service_logger.log_business_event(
                "关键词订阅用户收件箱关联处理完成",
                {
                    "keyword": keyword,
                    "keyword_id": keyword_record.id,
                    "total_users": len(unique_users),
                    "processed_users": processed_users,
                    "failed_users": failed_users,
                    "video_count": len(video_data_list)
                }
            )

        except Exception as e:
            error_msg = f"处理关键词订阅用户收件箱关联失败: {str(e)}"
            sync_result.errors.append(error_msg)
            self.error_tracker.track_error("keyword_inbox_relation_processing", e)
            self.service_logger.log_operation_error("处理关键词订阅用户收件箱关联", e)

    async def sync_keyword_videos(
        self,
        keyword: str,
        enable_inbox_relation: bool = True,
        user_uuid: Optional[str] = None
    ) -> KeywordSyncResponse:
        """
        同步关键词相关的视频列表

        功能：
        1. 计算关键词哈希，检查 trendinsight_keyword 表中是否存在，不存在则创建
        2. 使用 TrendInsight RPC 关键词搜索视频接口查询相关视频
        3. 智能批量处理 DouyinAweme 表记录（创建和更新）
        4. 在 trendinsight_video_related 表中创建关联记录（不存在才创建）
        5. 可选：自动为所有订阅该关键词的用户处理收件箱关联

        Args:
            keyword: 搜索关键词文本
            enable_inbox_relation: 是否启用收件箱关联处理（默认True）
            user_uuid: 用户UUID（已弃用，保留用于向后兼容）

        Returns:
            KeywordSyncResponse: 同步结果信息

        Example:
            {
                "keyword_action": "created",
                "keyword_data": {
                    "id": 123,
                    "keyword": "科技前沿",
                    "keyword_hash": "abc123def456",
                    "video_count": 25,
                    "created_at": "2025-01-01T00:00:00",
                    "updated_at": "2025-01-01T00:00:00"
                },
                "videos_synced": 25,
                "videos_failed": 0,
                "relations_created": 20,
                "relations_existing": 5,
                "video_items": [
                    {
                        "aweme_id": "7123456789012345678",
                        "aweme_type": "video",
                        "title": "科技前沿视频",
                        "desc": "这是一个关于科技前沿的视频",
                        "create_time": 1705314645,
                        "user_id": "123456789",
                        "nickname": "科技博主",
                        "liked_count": "1000",
                        "comment_count": "50",
                        "share_count": "20",
                        "collected_count": "30",
                        "source_keyword": "科技前沿"
                    }
                ],
                "errors": []
            }

        Raises:
            HTTPException: 当同步过程中发生致命错误时
        """
        operation_id = self.performance_monitor.start_operation("sync_keyword_videos")

        try:
            # 参数验证
            self.validate_required_params(keyword=keyword)

            self.service_logger.log_business_event(
                "开始关键词视频同步",
                {
                    "keyword": keyword,
                    "enable_inbox_relation": enable_inbox_relation,
                    "user_uuid": user_uuid
                }
            )

            # 初始化 KeywordSyncResponse 实例，提供类型安全性
            sync_result = KeywordSyncResponse(
                keyword_action=KeywordActionType.EXISTING,  # 默认值，后续会更新
                keyword_data=None,
                videos_synced=0,
                videos_failed=0,
                relations_created=0,
                relations_existing=0,
                video_items=[],
                errors=[],
            )

            # 1. 计算关键词哈希并检查是否存在
            keyword_hash: str = self._calculate_keyword_hash(keyword)
            existing_keyword: Optional[TrendInsightKeyword] = await TrendInsightKeyword.filter(
                keyword_hash=keyword_hash
            ).first()

            keyword_record: Optional[TrendInsightKeyword] = None
            if existing_keyword:
                keyword_record = existing_keyword
                sync_result.keyword_action = KeywordActionType.EXISTING
                sync_result.keyword_data = TrendInsightKeywordMapper.keyword_model_to_data(existing_keyword)

                self.service_logger.log_business_event(
                    "关键词已存在",
                    {"keyword": keyword, "keyword_id": existing_keyword.id}
                )
            else:
                # 创建新的关键词记录
                try:
                    new_keyword = await TrendInsightKeyword.create(
                        keyword=keyword,
                        keyword_hash=keyword_hash,
                        video_count=0,
                    )

                    keyword_record = new_keyword
                    sync_result.keyword_action = KeywordActionType.CREATED
                    sync_result.keyword_data = TrendInsightKeywordMapper.keyword_model_to_data(new_keyword)

                    self.service_logger.log_business_event(
                        "关键词创建成功",
                        {"keyword": keyword, "keyword_id": new_keyword.id}
                    )

                except Exception as e:
                    error_msg: str = f"创建关键词记录失败: {str(e)}"
                    sync_result.errors.append(error_msg)
                    self.error_tracker.track_error("keyword_creation", e)
                    self.service_logger.log_operation_error("创建关键词记录", e)
                    return sync_result

            # 2. 使用 TrendInsight RPC 搜索关键词相关视频
            try:
                video_search_response = await self.search_info_by_keyword(
                    keyword=keyword, author_ids=None, category_id="0", date_type=0, label_type=0, duration_type=0
                )

                if video_search_response and video_search_response.is_success and video_search_response.video_items:
                    # 3. 智能批量处理 DouyinAweme 表记录（创建和更新）
                    filtered_video_data_list, filtered_video_ids = await self._process_video_data(
                        video_search_response.video_items, keyword, sync_result
                    )

                    if not filtered_video_ids:
                        error_msg = f"搜索结果中没有有效的视频ID: {keyword}"
                        sync_result.errors.append(error_msg)
                        self.service_logger.log_business_event("视频过滤结果为空", {"keyword": keyword})
                        return sync_result

                    # 4. 处理关联关系
                    await self._process_video_relations(
                        keyword_record, filtered_video_ids, sync_result
                    )

                    # 5. 更新关键词视频计数
                    await self._update_keyword_video_count(
                        keyword_record, filtered_video_ids, sync_result
                    )

                    # 6. 可选：处理收件箱关联（为所有订阅用户）
                    if enable_inbox_relation:
                        await self._process_inbox_relation(
                            keyword_record, keyword, filtered_video_data_list, sync_result
                        )
                else:
                    error_msg = f"关键词搜索视频结果为空: {keyword}"
                    sync_result.errors.append(error_msg)
                    self.service_logger.log_business_event("搜索结果为空", {"keyword": keyword})

            except Exception as e:
                error_msg = f"搜索关键词视频失败: {str(e)}"
                sync_result.errors.append(error_msg)
                self.error_tracker.track_error("keyword_video_search", e)
                self.service_logger.log_operation_error("搜索关键词视频", e)

            self.service_logger.log_business_event(
                "关键词视频同步完成",
                {
                    "keyword": keyword,
                    "keyword_action": sync_result.keyword_action.value,
                    "videos_synced": sync_result.videos_synced,
                    "relations_created": sync_result.relations_created,
                    "relations_existing": sync_result.relations_existing,
                    "errors_count": len(sync_result.errors)
                }
            )

            return sync_result

        except Exception as e:
            self.error_tracker.track_error("sync_keyword_videos", e)
            raise HTTPException(status_code=500, detail=f"同步关键词和视频失败: {str(e)}")
        finally:
            self.performance_monitor.finish_operation(operation_id)


# 创建控制器实例
keyword_sync_controller = KeywordSyncController()
