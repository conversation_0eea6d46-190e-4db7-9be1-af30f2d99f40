"""
TrendInsight 主控制器

提供 TrendInsight 平台的基础API调用功能，包括用户信息查询、达人搜索、视频指数等
"""

import logging
from typing import List, Optional

from fastapi import HTTPException

from rpc.trendinsight import AsyncTrendInsightAPI, client_manager
from rpc.trendinsight.config import TrendInsightConfig
from rpc.trendinsight.schemas import (
    AuthorDetailResponse,
    DarenSearchRequest,
    DarenSearchResponse,
    UserInfoResponse,
    VideoIndexResponse,
    VideoSearchResponse,
)


class TrendInsightMainController:
    """TrendInsight 主控制器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = TrendInsightConfig()

    async def _get_trendinsight_client(self) -> AsyncTrendInsightAPI:
        """
        获取 TrendInsight 异步客户端实例

        Returns:
            AsyncTrendInsightAPI: 配置好账号提供者的异步客户端实例
        """
        # 通过 client_manager 创建客户端，cookies 由账号提供者自动管理
        async_client = client_manager.create_async_client()
        return AsyncTrendInsightAPI(async_client=async_client)

    async def query_user_self_info(self) -> UserInfoResponse:
        """
        查询用户自己的信息

        Returns:
            UserInfoResponse: 用户信息响应
        """
        try:
            client = await self._get_trendinsight_client()
            response = await client.query_user_self_info()
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"查询用户信息失败: {str(e)}")

    async def query_daren_sug_great_user_list(self, request: DarenSearchRequest) -> DarenSearchResponse:
        """
        查询达人的信息

        Args:
            request: 达人搜索请求参数

        Returns:
            DarenSearchResponse: 达人搜索响应
        """
        try:
            client = await self._get_trendinsight_client()
            response = await client.query_daren_sug_great_user_list(request)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"查询达人信息失败: {str(e)}")

    async def search_info_by_keyword(
        self,
        keyword: str,
        author_ids: Optional[List[str]] = None,
        category_id: str = "0",
        date_type: int = 0,
        label_type: int = 0,
        duration_type: int = 0,
    ) -> VideoSearchResponse:
        """
        关键字搜索视频接口

        Args:
            keyword: 搜索关键词
            author_ids: 作者ID列表，可选
            category_id: 分类ID，默认为"0"
            date_type: 日期类型，默认为0
            label_type: 标签类型，默认为0
            duration_type: 时长类型，默认为0

        Returns:
            VideoSearchResponse: 视频搜索响应
        """
        try:
            client = await self._get_trendinsight_client()

            response = await client.search_info_by_keyword(
                keyword=keyword,
                author_ids=author_ids,
                category_id=category_id,
                date_type=date_type,
                label_type=label_type,
                duration_type=duration_type,
            )
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"搜索视频失败: {str(e)}")

    async def get_author_detail(self, user_id: str) -> AuthorDetailResponse:
        """
        获取作者详情接口

        Args:
            user_id: 用户ID

        Returns:
            AuthorDetailResponse: 作者详情响应
        """
        try:
            client = await self._get_trendinsight_client()
            response = await client.get_author_detail(user_id)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取作者详情失败: {str(e)}")

    async def get_video_index(self, item_id: str, start_date: str, end_date: str) -> VideoIndexResponse:
        """
        获取视频指数数据接口

        Args:
            item_id: 视频ID
            start_date: 开始日期，格式：YYYYMMDD
            end_date: 结束日期，格式：YYYYMMDD

        Returns:
            VideoIndexResponse: 视频指数响应
        """
        try:
            client = await self._get_trendinsight_client()
            response = await client.get_video_index(item_id, start_date, end_date)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取视频指数失败: {str(e)}")

    async def pong(self) -> bool:
        """
        测试接口是否可用

        Returns:
            bool: 接口是否可用
        """
        try:
            client = await self._get_trendinsight_client()
            response = await client.pong()
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"测试接口失败: {str(e)}")


# 创建控制器实例
trendinsight_main_controller = TrendInsightMainController()

# 向后兼容的别名
TrendInsightController = TrendInsightMainController
