# Controllers/Douyin 新架构说明

## 概述

将原有的单体 `controllers/douyin.py` 文件重构为模块化的架构，将精选页面(jingxuan)和移动端(mobile)视频获取逻辑组织到合理的控制器结构中。

## 文件结构

```
controllers/douyin/
├── __init__.py              # 模块导出
├── html_controller.py       # HTML方式数据获取控制器
├── video_controller.py      # RPC方式数据获取控制器  
├── main_controller.py       # 统一主控制器
└── examples/               # 使用示例
    ├── usage_examples.py   # 详细使用示例
    ├── quick_test.py       # 快速测试脚本
    └── README.md          # 本文件
```

## 控制器说明

### 1. DouyinHTMLController (html_controller.py)
- **职责**: 基于HTML页面的数据获取
- **主要方法**:
  - `fetch_jingxuan_video_data()` - 精选页面数据获取
  - `fetch_mobile_video_data()` - 移动端分享页面数据获取
  - `fetch_pc_video_data()` - PC端页面数据获取

### 2. DouyinVideoController (video_controller.py)  
- **职责**: 基于RPC接口的数据获取
- **主要方法**:
  - `fetch_video_by_rpc()` - RPC方式获取视频
  - `validate_cookies()` - 验证cookies有效性
  - `refresh_cookies()` - 刷新cookies

### 3. DouyinController (main_controller.py)
- **职责**: 统一入口和智能调度
- **主要功能**:
  - 整合HTML和RPC控制器
  - 智能方法选择和故障转移
  - 批量处理能力
  - 全局配置管理

## 使用方法

### 基础使用

```python
from controllers.douyin import douyin_controller

# 精选页面方式获取
result = await douyin_controller.fetch_jingxuan_data(
    aweme_id="7123456789012345678",
    use_proxy=True,
    save_to_db=True
)

# 移动端方式获取  
result = await douyin_controller.fetch_mobile_data(
    aweme_id="7123456789012345678",
    use_proxy=True
)
```

### 智能自动获取

```python
# 智能选择最佳方法，自动故障转移
result = await douyin_controller.fetch_video_data_auto(
    aweme_id="7123456789012345678",
    preferred_method="jingxuan",      # 首选方法
    fallback_methods=["mobile", "rpc"], # 备用方法
    use_proxy=True
)
```

### 批量处理

```python
# 批量获取多个视频
aweme_ids = ["7123456789012345678", "7123456789012345679"]
results = await douyin_controller.batch_fetch_video_data(
    aweme_ids=aweme_ids,
    method="auto",
    max_concurrent=3
)
```

### 直接使用子控制器

```python
from controllers.douyin import DouyinHTMLController

# 直接创建HTML控制器
html_controller = DouyinHTMLController()
result = await html_controller.fetch_jingxuan_video_data(
    aweme_id="7123456789012345678"
)

# 或通过主控制器获取
html_controller = douyin_controller.get_html_controller()
```

## 配置选项

### HTML控制器配置

```python
from rpc.douyin.html_handler.config import DouyinHTMLConfig

config = DouyinHTMLConfig(
    request_timeout=30,
    max_retries=5,
    enable_proxy=True,
    enable_cookie_rotation=True,
    enable_anti_crawler_detection=True
)

html_controller = DouyinHTMLController(config=config)
```

### 请求级别配置

```python
result = await douyin_controller.fetch_jingxuan_data(
    aweme_id="7123456789012345678",
    use_proxy=False,
    custom_headers={
        "User-Agent": "custom-agent",
        "Accept-Language": "zh-CN,zh;q=0.9"
    },
    timeout=25,
    save_to_db=False
)
```

## 错误处理

```python
result = await douyin_controller.fetch_video_data_auto(
    aweme_id="7123456789012345678"
)

if result["success"]:
    # 成功处理
    data = result["data"]
    method = result["method"]  # 使用的方法
    source = result["source"]  # 数据来源
else:
    # 错误处理
    error = result["error"]
    methods_tried = result.get("methods_tried", [])
    print(f"获取失败: {error}, 尝试过的方法: {methods_tried}")
```

## 兼容性说明

- **向后兼容**: 新架构与现有代码完全兼容
- **渐进迁移**: 可以逐步迁移现有代码到新架构
- **现有功能**: 原有 `controllers/douyin.py` 中的所有功能都保留

## 性能优化

1. **智能缓存**: 自动缓存有效的cookies和配置
2. **并发控制**: 批量处理时的并发数量控制
3. **故障转移**: 自动在不同方法间切换以提高成功率
4. **资源管理**: 自动管理HTTP连接和代理资源

## 测试和调试

### 快速测试
```bash
cd controllers/douyin/examples
python quick_test.py
```

### 详细示例
```bash
cd controllers/douyin/examples  
python usage_examples.py
```

### 单元测试
确保在项目根目录运行：
```bash
pytest tests/ -k douyin
```

## 最佳实践

1. **使用全局实例**: 优先使用 `douyin_controller` 全局实例
2. **智能获取**: 使用 `fetch_video_data_auto()` 获得最佳成功率
3. **错误处理**: 总是检查返回结果的 `success` 字段
4. **代理使用**: 生产环境建议启用代理以提高稳定性
5. **批量处理**: 大量数据建议使用批量方法以提高效率

## 故障排查

### 常见问题

1. **导入错误**: 确保项目根目录在Python路径中
2. **网络超时**: 调整timeout参数或启用代理
3. **数据解析失败**: 检查返回的HTML结构是否发生变化
4. **代理连接失败**: 验证代理服务器设置

### 调试模式

```python
# 启用详细日志
import logging
logging.getLogger("controllers.douyin").setLevel(logging.DEBUG)

# 查看详细响应信息
result = await douyin_controller.fetch_jingxuan_data(aweme_id, debug=True)
print(result["html_response"])  # 详细的HTTP响应信息
```

## 开发指南

如需扩展新功能：

1. **新的获取方法**: 在对应控制器中添加方法
2. **新的数据源**: 可以创建新的专用控制器
3. **新的配置**: 在config中添加相应选项
4. **新的错误处理**: 在异常处理中添加对应逻辑

## 更新日志

- **v1.0**: 初始模块化架构
- **TODO**: 
  - 添加更多数据源支持
  - 增强错误重试机制
  - 优化缓存策略