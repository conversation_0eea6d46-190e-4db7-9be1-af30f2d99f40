"""
抖音数据获取服务
每个方法都有明确的输入输出类型，步骤清晰
"""

from typing import Any, Dict, Optional

from loguru import logger

from mappers.douyin.pydantic_models import DouyinVideoData

from .models import (
    HTMLFetchResult,
    JingxuanDataFetchResult,
    JSONParseResult,
    MobileDataFetchResult,
    ModelConversionResult,
    RPCDataFetchResult,
)


class DouyinHTMLService:
    """抖音HTML获取服务"""

    async def fetch_mobile_html(
        self, aweme_id: str, use_proxy: bool = True, custom_headers: Optional[Dict[str, str]] = None, timeout: int = 30
    ) -> HTMLFetchResult:
        """
        获取移动端HTML内容

        Args:
            aweme_id: 视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 超时时间

        Returns:
            HTMLFetchResult: HTML获取结果
        """
        try:
            from rpc.douyin.html_handler.client import html_client
            from rpc.douyin.html_handler.schemas import MobileShareRequest

            request = MobileShareRequest(
                aweme_id=aweme_id, use_proxy=use_proxy, custom_headers=custom_headers or {}, timeout=timeout
            )

            response = await html_client.fetch_mobile_share_page(request)

            if response.success and response.content:
                return HTMLFetchResult(
                    success=True,
                    content=response.content,
                    status_code=getattr(response, "status_code", 200),
                    response_time=getattr(response, "response_time", None),
                )
            else:
                return HTMLFetchResult(
                    success=False,
                    error_message=response.error_message or "Unknown error",
                    status_code=getattr(response, "status_code", None),
                )

        except Exception as e:
            logger.error(f"移动端HTML获取失败 {aweme_id}: {e}")
            return HTMLFetchResult(success=False, error_message=f"HTML获取异常: {str(e)}")

    async def fetch_jingxuan_html(
        self, aweme_id: str, use_proxy: bool = True, custom_headers: Optional[Dict[str, str]] = None, timeout: int = 30
    ) -> HTMLFetchResult:
        """
        获取精选页面HTML内容

        Args:
            aweme_id: 视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 超时时间

        Returns:
            HTMLFetchResult: HTML获取结果
        """
        try:
            from rpc.douyin.html_handler.client import html_client
            from rpc.douyin.html_handler.schemas import JingxuanRequest

            request = JingxuanRequest(
                aweme_id=aweme_id, use_proxy=use_proxy, custom_headers=custom_headers or {}, timeout=timeout
            )

            response = await html_client.fetch_jingxuan_page(request)

            if response.success and response.content:
                return HTMLFetchResult(
                    success=True,
                    content=response.content,
                    status_code=getattr(response, "status_code", 200),
                    response_time=getattr(response, "response_time", None),
                )
            else:
                return HTMLFetchResult(
                    success=False,
                    error_message=response.error_message or "Unknown error",
                    status_code=getattr(response, "status_code", None),
                )

        except Exception as e:
            logger.error(f"精选页面HTML获取失败 {aweme_id}: {e}")
            return HTMLFetchResult(success=False, error_message=f"HTML获取异常: {str(e)}")


class DouyinParseService:
    """抖音数据解析服务"""

    def parse_mobile_json(self, html_content: str, aweme_id: str) -> JSONParseResult:
        """
        从移动端HTML中解析JSON数据

        Args:
            html_content: HTML内容
            aweme_id: 视频ID

        Returns:
            JSONParseResult: JSON解析结果
        """
        try:
            from utils.douyin.extract.html_parser import parse_mobile_html

            parsed_data = parse_mobile_html(html_content, aweme_id)

            if parsed_data:
                return JSONParseResult(
                    success=True,
                    data=parsed_data,
                    raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
                )
            else:
                return JSONParseResult(
                    success=False,
                    error_message="未能从HTML中解析出有效的JSON数据",
                    raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
                )

        except Exception as e:
            logger.error(f"移动端JSON解析失败 {aweme_id}: {e}")
            return JSONParseResult(
                success=False,
                error_message=f"JSON解析异常: {str(e)}",
                raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
            )

    def parse_jingxuan_json(self, html_content: str, aweme_id: str) -> JSONParseResult:
        """
        从精选页面HTML中解析JSON数据

        Args:
            html_content: HTML内容
            aweme_id: 视频ID

        Returns:
            JSONParseResult: JSON解析结果
        """
        try:
            from utils.douyin.extract.html_parser import parse_jingxuan_html

            parsed_data = parse_jingxuan_html(html_content, aweme_id)

            if parsed_data:
                return JSONParseResult(
                    success=True,
                    data=parsed_data,
                    raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
                )
            else:
                return JSONParseResult(
                    success=False,
                    error_message="未能从HTML中解析出有效的pace_f数据",
                    raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
                )

        except Exception as e:
            logger.error(f"精选页面JSON解析失败 {aweme_id}: {e}")
            return JSONParseResult(
                success=False,
                error_message=f"JSON解析异常: {str(e)}",
                raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
            )


class DouyinModelService:
    """抖音数据模型转换服务"""

    def convert_to_douyin_video_data(self, raw_data: Dict[str, Any], source: str = "unknown") -> ModelConversionResult:
        """
        将原始数据转换为DouyinVideoData模型

        Args:
            raw_data: 原始数据字典
            source: 数据来源 ("mobile", "jingxuan", "rpc")

        Returns:
            ModelConversionResult: 模型转换结果
        """
        try:
            # 使用对应的映射器转换数据
            if source == "mobile":
                from mappers.douyin.mobile_mapper import MobileDataMapper

                mapper = MobileDataMapper()
            elif source == "jingxuan":
                from mappers.douyin.jingxuan_mapper import JingxuanDataMapper

                mapper = JingxuanDataMapper()
            elif source == "rpc":
                from mappers.douyin.rpc_mapper import RPCDataMapper

                mapper = RPCDataMapper()
            else:
                return ModelConversionResult(success=False, error_message=f"不支持的数据源: {source}")

            # 验证原始数据
            if not mapper.validate_raw_data(raw_data):
                return ModelConversionResult(
                    success=False,
                    error_message="原始数据验证失败",
                    validation_errors={"raw_data": "数据格式不符合要求"},
                )

            # 转换为标准格式
            standard_data = mapper.map_to_standard_format(raw_data)

            # 创建DouyinVideoData模型实例
            video_data_model = self._create_douyin_video_data_model(standard_data)

            return ModelConversionResult(success=True, model=video_data_model)

        except Exception as e:
            logger.error(f"模型转换失败 source={source}: {e}")
            return ModelConversionResult(success=False, error_message=f"模型转换异常: {str(e)}")

    def _create_douyin_video_data_model(self, standard_data: Dict[str, Any]) -> DouyinVideoData:
        """
        根据标准化数据创建DouyinVideoData模型实例

        Args:
            standard_data: 标准化的数据字典

        Returns:
            DouyinVideoData: 创建的模型实例
        """
        # 直接使用Pydantic模型进行数据验证和创建
        return DouyinVideoData(**standard_data)


class DouyinDataService:
    """抖音完整数据获取服务 - 组合各个步骤"""

    def __init__(self):
        self.html_service = DouyinHTMLService()
        self.parse_service = DouyinParseService()
        self.model_service = DouyinModelService()

    async def fetch_mobile_data(
        self, aweme_id: str, use_proxy: bool = True, custom_headers: Optional[Dict[str, str]] = None, timeout: int = 30
    ) -> MobileDataFetchResult:
        """
        获取移动端数据的完整流程

        Args:
            aweme_id: 视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 超时时间

        Returns:
            MobileDataFetchResult: 完整的获取结果
        """
        result = MobileDataFetchResult(success=False, aweme_id=aweme_id)

        # 步骤1: 获取HTML内容
        logger.info(f"步骤1: 获取移动端HTML内容 {aweme_id}")
        html_result = await self.html_service.fetch_mobile_html(aweme_id, use_proxy, custom_headers, timeout)
        result.html_result = html_result

        if not html_result.success:
            result.failed_step = "html_fetch"
            result.error_message = f"HTML获取失败: {html_result.error_message}"
            return result

        # 步骤2: 解析HTML中的JSON
        logger.info(f"步骤2: 解析移动端JSON数据 {aweme_id}")
        json_result = self.parse_service.parse_mobile_json(html_result.content, aweme_id)
        result.json_result = json_result

        if not json_result.success:
            result.failed_step = "json_parse"
            result.error_message = f"JSON解析失败: {json_result.error_message}"
            return result

        # 步骤3: 转换为数据库模型
        logger.info(f"步骤3: 转换为DouyinVideoData模型 {aweme_id}")
        model_result = self.model_service.convert_to_douyin_video_data(json_result.data, "mobile")
        result.model_result = model_result

        if not model_result.success:
            result.failed_step = "model_conversion"
            result.error_message = f"模型转换失败: {model_result.error_message}"
            return result

        # 成功完成所有步骤
        result.success = True
        result.final_model = model_result.model
        logger.info(f"移动端数据获取成功 {aweme_id}")

        return result

    async def fetch_jingxuan_data(
        self, aweme_id: str, use_proxy: bool = True, custom_headers: Optional[Dict[str, str]] = None, timeout: int = 30
    ) -> JingxuanDataFetchResult:
        """
        获取精选页面数据的完整流程

        Args:
            aweme_id: 视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 超时时间

        Returns:
            JingxuanDataFetchResult: 完整的获取结果
        """
        result = JingxuanDataFetchResult(success=False, aweme_id=aweme_id)

        # 步骤1: 获取HTML内容
        logger.info(f"步骤1: 获取精选页面HTML内容 {aweme_id}")
        html_result = await self.html_service.fetch_jingxuan_html(aweme_id, use_proxy, custom_headers, timeout)
        result.html_result = html_result

        if not html_result.success:
            result.failed_step = "html_fetch"
            result.error_message = f"HTML获取失败: {html_result.error_message}"
            return result

        # 步骤2: 解析HTML中的JSON
        logger.info(f"步骤2: 解析精选页面JSON数据 {aweme_id}")
        json_result = self.parse_service.parse_jingxuan_json(html_result.content, aweme_id)
        result.json_result = json_result

        if not json_result.success:
            result.failed_step = "json_parse"
            result.error_message = f"JSON解析失败: {json_result.error_message}"
            return result

        # 步骤3: 转换为数据库模型
        logger.info(f"步骤3: 转换为DouyinVideoData模型 {aweme_id}")
        model_result = self.model_service.convert_to_douyin_video_data(json_result.data, "jingxuan")
        result.model_result = model_result

        if not model_result.success:
            result.failed_step = "model_conversion"
            result.error_message = f"模型转换失败: {model_result.error_message}"
            return result

        # 成功完成所有步骤
        result.success = True
        result.final_model = model_result.model
        logger.info(f"精选页面数据获取成功 {aweme_id}")

        return result

    async def fetch_rpc_data(self, aweme_id: str, cookies: Optional[str] = None) -> RPCDataFetchResult:
        """
        获取RPC数据的完整流程

        Args:
            aweme_id: 视频ID
            cookies: 可选的cookies

        Returns:
            RPCDataFetchResult: 完整的获取结果
        """
        result = RPCDataFetchResult(success=False, aweme_id=aweme_id)

        try:
            # 步骤1: 调用RPC接口
            logger.info(f"步骤1: 调用RPC接口获取数据 {aweme_id}")
            from .controller import DouyinController

            controller = DouyinController()
            rpc_response = await controller.get_video_detail(video_id=aweme_id, cookies=cookies, response_type="db")
            result.rpc_response = rpc_response

            if not rpc_response:
                result.failed_step = "rpc_call"
                result.error_message = "RPC调用返回空结果"
                return result

            # 步骤2: 转换RPC数据为DouyinVideoData模型
            logger.info(f"步骤2: 转换RPC数据为DouyinVideoData模型 {aweme_id}")
            model_result = self.model_service.convert_to_douyin_video_data(rpc_response, "rpc")
            result.model_result = model_result

            if not model_result.success:
                result.failed_step = "model_conversion"
                result.error_message = f"模型转换失败: {model_result.error_message}"
                return result

            # 成功完成所有步骤
            result.success = True
            result.final_model = model_result.model
            logger.info(f"RPC数据获取成功 {aweme_id}")

            return result

        except Exception as e:
            logger.error(f"RPC数据获取失败 {aweme_id}: {e}")
            result.failed_step = "rpc_call"
            result.error_message = f"RPC调用异常: {str(e)}"
            return result
