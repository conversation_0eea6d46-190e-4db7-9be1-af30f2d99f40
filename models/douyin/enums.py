"""
抖音平台枚举定义
"""

from models.enums import EnumBase


class DouyinVideoStatus(EnumBase):
    """抖音视频状态枚举"""

    NORMAL = "normal"  # 正常
    DELETED = "deleted"  # 已删除
    PRIVATE = "private"  # 私密
    REVIEWING = "reviewing"  # 审核中
    PROHIBITED = "prohibited"  # 被禁止


class DouyinUserVerifyType(EnumBase):
    """抖音用户认证类型"""

    NONE = "none"  # 无认证
    PERSONAL = "personal"  # 个人认证
    ENTERPRISE = "enterprise"  # 企业认证
    GOVERNMENT = "government"  # 政府认证
    MEDIA = "media"  # 媒体认证
