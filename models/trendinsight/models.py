# -*- coding: utf-8 -*-
# <AUTHOR> MediaCrawler Pro Team
# @Time    : 2025/01/16
# @Desc    : 巨量引擎平台 Tortoise ORM 模型

import random
import re
import time
from datetime import datetime, timedelta
from urllib.parse import urlparse

from tortoise import fields

from models.base import BaseModel
import logging

# 添加日志记录器
logger = logging.getLogger(__name__)


class TrendInsightVideo(BaseModel):
    """巨量引擎视频模型 - 简化版"""

    # 主键字段 - 覆盖 BaseModel 的 id 字段
    id = fields.CharField(max_length=64, pk=True, description="抖音视频ID (aweme_id)")

    # 趋势评分
    trend_score = fields.FloatField(default=0.0, description="趋势评分")
    trend_radio = fields.FloatField(default=0.0, description="趋势评分系数")

    @property
    def aweme_id(self):
        """为了向后兼容，提供 aweme_id 属性"""
        return self.id

    @aweme_id.setter
    def aweme_id(self, value):
        """为了向后兼容，提供 aweme_id 设置器"""
        self.id = value

    class Meta:
        table = "trendinsight_video"
        indexes = [
            ("id",),
            ("trend_score",),
            ("created_at",),
            ("updated_at",),
        ]


class TrendInsightAuthor(BaseModel):
    """巨量引擎作者模型 - 基于API JSON结构"""

    # 基本信息 (直接映射API字段)
    user_id = fields.CharField(max_length=64, unique=True, description="用户ID")
    user_name = fields.CharField(max_length=100, description="用户名")
    user_head_logo = fields.CharField(max_length=1000, null=True, description="用户头像")
    user_gender = fields.CharField(max_length=10, null=True, description="用户性别")
    user_location = fields.CharField(max_length=200, null=True, description="用户位置")
    user_introduction = fields.TextField(null=True, description="用户简介")

    # 统计数据 (API返回字符串，转换为数值存储)
    item_count = fields.CharField(max_length=20, description="作品数量(原始)")
    fans_count = fields.CharField(max_length=20, description="粉丝数(原始)")
    like_count = fields.CharField(max_length=20, description="点赞数(原始)")

    # 转换后的数值字段
    item_count_int = fields.IntField(default=0, description="作品数量(数值)")
    fans_count_int = fields.IntField(default=0, description="粉丝数(数值)")
    like_count_int = fields.BigIntField(default=0, description="点赞数(数值)")

    # 标签分类
    first_tag_name = fields.CharField(max_length=100, null=True, description="第一标签名称")
    second_tag_name = fields.CharField(max_length=100, null=True, description="第二标签名称")

    # 粉丝里程碑
    fans_milestone_create_time = fields.CharField(max_length=20, null=True, description="粉丝里程碑创建时间")

    # 抖音平台字段
    aweme_id = fields.CharField(max_length=100, null=True, description="抖音ID")
    user_aweme_url = fields.CharField(max_length=1000, null=True, description="抖音用户链接")
    douyin_user_id = fields.CharField(max_length=128, null=True, blank=True, description="抖音用户ID")
    aweme_pic = fields.CharField(max_length=1000, null=True, description="抖音头像图片")

    # 系统字段
    platform = fields.CharField(max_length=20, default="trendinsight", description="来源平台")
    crawl_time = fields.BigIntField(description="爬取时间戳")
    source_keyword = fields.CharField(max_length=200, default="", description="搜索来源关键字")

    # 扩展字段
    raw_data = fields.TextField(null=True, description="原始数据(JSON格式)")

    class Meta:
        table = "trendinsight_author"
        indexes = [
            ("user_id",),
            ("user_name",),
            ("platform",),
            ("fans_count_int",),
            ("item_count_int",),
            ("like_count_int",),
            ("crawl_time",),
            ("source_keyword",),
            ("aweme_id",),
            ("douyin_user_id",),
            ("first_tag_name",),
            ("user_gender",),
        ]


class TrendInsightKeyword(BaseModel):
    """巨量引擎关键词模型"""

    # 关键词信息
    keyword = fields.CharField(max_length=200, description="关键词")
    keyword_hash = fields.CharField(max_length=64, unique=True, description="关键词哈希")

    # 统计信息
    video_count = fields.IntField(default=0, description="视频数量")

    class Meta:
        table = "trendinsight_keyword"
        indexes = [
            ("keyword",),
            ("keyword_hash",),
        ]


class TrendInsightVideoRelated(BaseModel):
    """巨量引擎视频关联关系模型"""

    # 关联字段
    source_id = fields.CharField(max_length=32, description="关键词或作者ID")
    source_type = fields.CharField(max_length=32, null=True, description="来源类型，关键词或作者")
    video_id = fields.CharField(max_length=64, description="视频ID (对应 TrendInsightVideo.id)")

    class Meta:
        table = "trendinsight_video_related"
        indexes = [
            ("source_id",),
            ("video_id",),
            ("source_id", "video_id"),  # 复合索引
        ]
        # 确保同一来源和视频只能有一条关联记录
        unique_together = [("source_id", "video_id")]


# 工具函数


def extract_douyin_user_id(user_aweme_url: str) -> str:
    """
    从抖音用户URL中提取用户ID

    Args:
        user_aweme_url: 抖音用户链接，格式如 https://www.douyin.com/user/{user_id}

    Returns:
        str: 提取的用户ID，如果提取失败返回空字符串

    Examples:
        >>> extract_douyin_user_id("https://www.douyin.com/user/MS4wLjABAAAA123")
        "MS4wLjABAAAA123"
        >>> extract_douyin_user_id("https://www.douyin.com/user/123456789")
        "123456789"
        >>> extract_douyin_user_id("invalid_url")
        ""
    """
    if not user_aweme_url or not isinstance(user_aweme_url, str):
        return ""

    try:
        # 使用正则表达式匹配抖音用户URL模式
        # 支持多种可能的URL格式
        patterns = [
            r"https?://(?:www\.)?douyin\.com/user/([^/?#&]+)",  # 标准格式
            r"https?://(?:www\.)?douyin\.com/user/([^/?#&]+)/?",  # 带尾部斜杠
            r"douyin\.com/user/([^/?#&]+)",  # 不带协议
        ]

        for pattern in patterns:
            match = re.search(pattern, user_aweme_url)
            if match:
                user_id = match.group(1)
                # 验证提取的用户ID不为空且不包含特殊字符
                if user_id and len(user_id.strip()) > 0:
                    return user_id.strip()

        # 如果正则匹配失败，尝试使用URL解析
        parsed_url = urlparse(user_aweme_url)
        if "douyin.com" in parsed_url.netloc and "/user/" in parsed_url.path:
            path_parts = parsed_url.path.strip("/").split("/")
            if len(path_parts) >= 2 and path_parts[0] == "user":
                user_id = path_parts[1]
                if user_id and len(user_id.strip()) > 0:
                    return user_id.strip()

        logger.warning(f"extract_douyin_user_id: 无法从URL中提取用户ID - {user_aweme_url}")
        return ""

    except Exception as e:
        logger.error(f"extract_douyin_user_id: 提取用户ID时发生错误 - {e}, URL: {user_aweme_url}")
        return ""


# 数据操作函数


async def update_trendinsight_video(video_data: dict) -> bool:
    """
    更新或创建 TrendInsight 视频数据

    Args:
        video_data: 视频数据字典，必须包含 aweme_id 字段

    Returns:
        bool: 操作是否成功
    """
    try:
        aweme_id = video_data.get("aweme_id")
        if not aweme_id:
            logger.error("update_trendinsight_video: aweme_id 不能为空")
            return False

        # 准备数据
        update_data = {
            "trend_score": video_data.get("trend_score", 0.0),
        }

        # 使用 update_or_create 方法
        video, created = await TrendInsightVideo.update_or_create(id=aweme_id, defaults=update_data)

        # 如果是新创建的记录，为 trend_radio 赋值随机数
        if created:
            # 生成 2.69 到 2.99 之间的随机数
            trend_radio_value = round(random.uniform(2.69, 2.99), 2)
            video.trend_radio = trend_radio_value
            await video.save()

        action = "创建" if created else "更新"
        logger.info(f"update_trendinsight_video: {action}视频数据成功 - aweme_id: {aweme_id}")
        return True

    except Exception as e:
        logger.error(f"update_trendinsight_video: 操作失败 - {e}")
        return False


async def update_trendinsight_author(author_data: dict) -> bool:
    """
    更新或创建 TrendInsight 作者数据

    Args:
        author_data: 作者数据字典（已处理好的 Tortoise ORM 数据）

    Returns:
        bool: 操作是否成功
    """
    try:
        user_id = author_data.get("user_id")
        if not user_id:
            logger.error("update_trendinsight_author: user_id 不能为空")
            return False

        # 使用 update_or_create 方法
        author, created = await TrendInsightAuthor.update_or_create(user_id=user_id, defaults=author_data)

        action = "创建" if created else "更新"
        logger.info(f"update_trendinsight_author: {action}作者数据成功 - user_id: {user_id}")
        return True

    except Exception as e:
        logger.error(f"update_trendinsight_author: 操作失败 - {e}")
        return False
